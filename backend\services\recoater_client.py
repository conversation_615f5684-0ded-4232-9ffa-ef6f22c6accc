"""
Recoater Client Service
======================

This module provides the RecoaterClient class that handles all communication
with the Aerosint SPD Recoater hardware API. It serves as the single point
of contact for hardware operations.

The client implements methods for all endpoints defined in the openapi.json
specification and provides proper error handling and type safety.
"""

import requests
from typing import Dict, Any, Optional
import logging
from requests.exceptions import RequestException, ConnectionError, Timeout

logger = logging.getLogger(__name__)


class RecoaterConnectionError(Exception):
    """Raised when connection to recoater hardware fails."""
    pass


class RecoaterAPIError(Exception):
    """Raised when recoater API returns an error response."""
    pass


class RecoaterClient:
    """
    Client for communicating with the Aerosint SPD Recoater hardware API.
    
    This class implements all the endpoints defined in the openapi.json
    specification and provides a clean interface for the backend to
    interact with the recoater hardware.
    """
    
    def __init__(self, base_url: str, timeout: float = 5.0):
        """
        Initialize the RecoaterClient.
        
        Args:
            base_url: The base URL of the recoater API (e.g., "http://*************:8080")
            timeout: Request timeout in seconds
        """
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        
        logger.info(f"RecoaterClient initialized with base_url: {self.base_url}")
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """
        Make a request to the recoater API with proper error handling.
        
        Args:
            method: HTTP method (GET, POST, PUT, DELETE)
            endpoint: API endpoint (without leading slash)
            **kwargs: Additional arguments for requests
            
        Returns:
            JSON response as dictionary
            
        Raises:
            RecoaterConnectionError: If connection fails
            RecoaterAPIError: If API returns error status
        """
        url = f"{self.base_url}/{endpoint.lstrip('/')}"
        
        try:
            logger.debug(f"Making {method} request to {url}")
            response = self.session.request(
                method=method,
                url=url,
                timeout=self.timeout,
                **kwargs
            )
            
            # Check for HTTP errors
            if response.status_code >= 400:
                logger.error(f"API error {response.status_code}: {response.text}")
                raise RecoaterAPIError(f"API returned status {response.status_code}: {response.text}")
            
            # Try to parse JSON response
            try:
                return response.json()
            except ValueError:
                # If response is not JSON, return empty dict
                return {}
                
        except (ConnectionError, Timeout) as e:
            logger.error(f"Connection error: {e}")
            raise RecoaterConnectionError(f"Failed to connect to recoater: {e}")
        except RequestException as e:
            logger.error(f"Request error: {e}")
            raise RecoaterConnectionError(f"Request failed: {e}")
    
    def get_state(self) -> Dict[str, Any]:
        """
        Get the current state of the recoater.
        
        Returns:
            Dictionary containing the recoater state information
        """
        return self._make_request("GET", "/state")
    
    def get_config(self) -> Dict[str, Any]:
        """
        Get the recoater configuration variables.
        
        Returns:
            Dictionary containing the recoater configuration
        """
        return self._make_request("GET", "/config")
    
    def set_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Set the recoater configuration variables.
        
        Args:
            config: Configuration dictionary
            
        Returns:
            Response from the API
        """
        return self._make_request("PUT", "/config", json=config)
    
    def get_drums(self) -> Dict[str, Any]:
        """
        Get information about all drums.
        
        Returns:
            Dictionary containing drums information
        """
        return self._make_request("GET", "/drums")
    
    def get_drum(self, drum_id: int) -> Dict[str, Any]:
        """
        Get information about a specific drum.
        
        Args:
            drum_id: The drum's ID
            
        Returns:
            Dictionary containing drum information
        """
        return self._make_request("GET", f"/drums/{drum_id}")
    
    def health_check(self) -> bool:
        """
        Perform a simple health check to verify connectivity.

        Returns:
            True if recoater is reachable and responding, False otherwise
        """
        try:
            self.get_state()
            return True
        except (RecoaterConnectionError, RecoaterAPIError):
            return False

    # Axis Control Methods
    # Note: These endpoints are assumed to exist based on the project requirements
    # and follow similar patterns to the drum motion endpoints

    def get_axis_status(self, axis: str) -> Dict[str, Any]:
        """
        Get the current status of a specific axis.

        Args:
            axis: The axis identifier ('x', 'z', or 'gripper')

        Returns:
            Dictionary containing axis status information
        """
        return self._make_request("GET", f"/axis/{axis}")

    def move_axis(self, axis: str, distance: float, speed: float, mode: str = "relative") -> Dict[str, Any]:
        """
        Move an axis by the specified distance.

        Args:
            axis: The axis identifier ('x' or 'z')
            distance: Distance to move in mm
            speed: Movement speed in mm/s
            mode: Movement mode ('relative', 'absolute', or 'homing')

        Returns:
            Response from the API
        """
        payload = {
            "mode": mode,
            "distance": distance,
            "speed": speed
        }
        return self._make_request("POST", f"/axis/{axis}/motion", json=payload)

    def home_axis(self, axis: str, speed: float = 10.0) -> Dict[str, Any]:
        """
        Home a specific axis.

        Args:
            axis: The axis identifier ('x' or 'z')
            speed: Homing speed in mm/s

        Returns:
            Response from the API
        """
        payload = {
            "mode": "homing",
            "speed": speed
        }
        return self._make_request("POST", f"/axis/{axis}/motion", json=payload)

    def get_axis_motion(self, axis: str) -> Dict[str, Any]:
        """
        Get the current motion command for an axis.

        Args:
            axis: The axis identifier ('x' or 'z')

        Returns:
            Dictionary containing current motion information
        """
        return self._make_request("GET", f"/axis/{axis}/motion")

    def cancel_axis_motion(self, axis: str) -> Dict[str, Any]:
        """
        Cancel the current motion command for an axis.

        Args:
            axis: The axis identifier ('x' or 'z')

        Returns:
            Response from the API
        """
        return self._make_request("DELETE", f"/axis/{axis}/motion")

    def set_gripper_state(self, enabled: bool) -> Dict[str, Any]:
        """
        Set the gripper state (punch gripper).

        Args:
            enabled: True to activate gripper, False to deactivate

        Returns:
            Response from the API
        """
        payload = {"enabled": enabled}
        return self._make_request("PUT", f"/axis/gripper/state", json=payload)

    def get_gripper_state(self) -> Dict[str, Any]:
        """
        Get the current gripper state.

        Returns:
            Dictionary containing gripper state information
        """
        return self._make_request("GET", f"/axis/gripper/state")

    # Drum Control Methods
    # These methods implement the drum control endpoints from the openapi.json specification

    def get_drum_motion(self, drum_id: int) -> Dict[str, Any]:
        """
        Get the current motion command for a drum.

        Args:
            drum_id: The drum's ID

        Returns:
            Dictionary containing current motion information
        """
        return self._make_request("GET", f"/drums/{drum_id}/motion")

    def set_drum_motion(self, drum_id: int, mode: str, speed: float, distance: float = None, turns: float = None) -> Dict[str, Any]:
        """
        Create a motion command for a drum.

        Args:
            drum_id: The drum's ID
            mode: Motion mode ('absolute', 'relative', 'turns', 'speed', 'homing')
            speed: The speed of the motion [mm/s]
            distance: The distance of the motion [mm] (for absolute/relative modes)
            turns: The number of turns (for turns mode)

        Returns:
            Response from the API
        """
        payload = {
            "mode": mode,
            "speed": speed
        }

        if distance is not None:
            payload["distance"] = distance
        if turns is not None:
            payload["turns"] = turns

        return self._make_request("POST", f"/drums/{drum_id}/motion", json=payload)

    def cancel_drum_motion(self, drum_id: int) -> Dict[str, Any]:
        """
        Cancel the current motion command for a drum.

        Args:
            drum_id: The drum's ID

        Returns:
            Response from the API
        """
        return self._make_request("DELETE", f"/drums/{drum_id}/motion")

    def get_drum_ejection(self, drum_id: int, unit: str = "pascal") -> Dict[str, Any]:
        """
        Get the ejection pressure information for a drum.

        Args:
            drum_id: The drum's ID
            unit: Pressure unit ('pascal' or 'bar')

        Returns:
            Dictionary containing ejection pressure information
        """
        params = {"unit": unit}
        return self._make_request("GET", f"/drums/{drum_id}/ejection", params=params)

    def set_drum_ejection(self, drum_id: int, target: float, unit: str = "pascal") -> Dict[str, Any]:
        """
        Set the target ejection pressure for a drum.

        Args:
            drum_id: The drum's ID
            target: Target ejection pressure
            unit: Pressure unit ('pascal' or 'bar')

        Returns:
            Response from the API
        """
        payload = {
            "target": target,
            "unit": unit
        }
        return self._make_request("PUT", f"/drums/{drum_id}/ejection", json=payload)

    def get_drum_suction(self, drum_id: int) -> Dict[str, Any]:
        """
        Get the suction pressure information for a drum.

        Args:
            drum_id: The drum's ID

        Returns:
            Dictionary containing suction pressure information
        """
        return self._make_request("GET", f"/drums/{drum_id}/suction")

    def set_drum_suction(self, drum_id: int, target: float) -> Dict[str, Any]:
        """
        Set the target suction pressure for a drum.

        Args:
            drum_id: The drum's ID
            target: Target suction pressure [Pa]

        Returns:
            Response from the API
        """
        payload = {"target": target}
        return self._make_request("PUT", f"/drums/{drum_id}/suction", json=payload)
