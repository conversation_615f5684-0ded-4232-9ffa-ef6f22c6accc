"""
Tests for Recoater Controls API endpoints.

This module contains comprehensive tests for the drum control API endpoints,
including motion control, ejection pressure, and suction pressure management.
"""

import pytest
from fastapi.testclient import TestClient
from unittest.mock import Mock, patch

from app.main import app
from services.recoater_client import RecoaterConnectionError, RecoaterAPIError

client = TestClient(app)

class TestRecoaterControlsAPI:
    """Test class for recoater controls API endpoints."""

    @patch('app.main.recoater_client')
    def test_get_drum_motion_success(self, mock_client):
        """Test successful drum motion status retrieval."""
        # Setup mock
        mock_client.get_drum_motion.return_value = {
            "mode": "relative",
            "speed": 30.0,
            "distance": 100.0
        }

        # Make request
        response = client.get("/api/v1/recoater/drums/0/motion")

        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert data["drum_id"] == 0
        assert data["connected"] is True
        assert "motion" in data
        assert data["motion"]["mode"] == "relative"
        mock_client.get_drum_motion.assert_called_once_with(0)

    @patch('app.main.recoater_client')
    def test_get_drum_motion_connection_error(self, mock_client):
        """Test drum motion status retrieval with connection error."""
        # Setup mock
        mock_client.get_drum_motion.side_effect = RecoaterConnectionError("Connection failed")

        # Make request
        response = client.get("/api/v1/recoater/drums/0/motion")

        # Assertions
        assert response.status_code == 503
        assert "Connection to recoater failed" in response.json()["detail"]

    @patch('app.main.recoater_client')
    def test_set_drum_motion_success(self, mock_client):
        """Test successful drum motion command."""
        # Setup mock
        mock_client.set_drum_motion.return_value = {"status": "motion_created"}

        # Make request
        motion_data = {
            "mode": "relative",
            "speed": 30.0,
            "distance": 100.0
        }
        response = client.post("/api/v1/recoater/drums/0/motion", json=motion_data)

        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert data["drum_id"] == 0
        assert data["connected"] is True
        assert data["motion_command"]["mode"] == "relative"
        mock_client.set_drum_motion.assert_called_once_with(
            drum_id=0, mode="relative", speed=30.0, distance=100.0, turns=None
        )

    @patch('app.main.recoater_client')
    def test_set_drum_motion_turns_mode(self, mock_client):
        """Test drum motion command with turns mode."""
        # Setup mock
        mock_client.set_drum_motion.return_value = {"status": "motion_created"}

        # Make request
        motion_data = {
            "mode": "turns",
            "speed": 30.0,
            "turns": 2.5
        }
        response = client.post("/api/v1/recoater/drums/0/motion", json=motion_data)

        # Assertions
        assert response.status_code == 200
        mock_client.set_drum_motion.assert_called_once_with(
            drum_id=0, mode="turns", speed=30.0, distance=None, turns=2.5
        )

    @patch('app.main.recoater_client')
    def test_set_drum_motion_api_error(self, mock_client):
        """Test drum motion command with API error."""
        # Setup mock
        mock_client.set_drum_motion.side_effect = RecoaterAPIError("Invalid motion parameters")

        # Make request
        motion_data = {
            "mode": "relative",
            "speed": 30.0,
            "distance": 100.0
        }
        response = client.post("/api/v1/recoater/drums/0/motion", json=motion_data)

        # Assertions
        assert response.status_code == 400
        assert "Recoater API error" in response.json()["detail"]

    @patch('app.main.recoater_client')
    def test_cancel_drum_motion_success(self, mock_client):
        """Test successful drum motion cancellation."""
        # Setup mock
        mock_client.cancel_drum_motion.return_value = {"status": "motion_cancelled"}

        # Make request
        response = client.delete("/api/v1/recoater/drums/0/motion")

        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert data["drum_id"] == 0
        assert data["action"] == "motion_cancelled"
        mock_client.cancel_drum_motion.assert_called_once_with(0)

    @patch('app.main.recoater_client')
    def test_get_drum_ejection_success(self, mock_client):
        """Test successful drum ejection pressure retrieval."""
        # Setup mock
        mock_client.get_drum_ejection.return_value = {
            "maximum": 300.0,
            "target": 200.0,
            "value": 150.0,
            "unit": "pascal"
        }

        # Make request
        response = client.get("/api/v1/recoater/drums/0/ejection?unit=pascal")

        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert data["drum_id"] == 0
        assert data["connected"] is True
        assert "ejection" in data
        assert data["ejection"]["target"] == 200.0
        mock_client.get_drum_ejection.assert_called_once_with(0, "pascal")

    @patch('app.main.recoater_client')
    def test_set_drum_ejection_success(self, mock_client):
        """Test successful drum ejection pressure setting."""
        # Setup mock
        mock_client.set_drum_ejection.return_value = {"status": "ejection_set"}

        # Make request
        ejection_data = {
            "target": 250.0,
            "unit": "pascal"
        }
        response = client.put("/api/v1/recoater/drums/0/ejection", json=ejection_data)

        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert data["drum_id"] == 0
        assert data["ejection_command"]["target"] == 250.0
        mock_client.set_drum_ejection.assert_called_once_with(
            drum_id=0, target=250.0, unit="pascal"
        )

    @patch('app.main.recoater_client')
    def test_get_drum_suction_success(self, mock_client):
        """Test successful drum suction pressure retrieval."""
        # Setup mock
        mock_client.get_drum_suction.return_value = {
            "maximum": 5.0,
            "target": 3.0,
            "value": 2.5
        }

        # Make request
        response = client.get("/api/v1/recoater/drums/0/suction")

        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert data["drum_id"] == 0
        assert data["connected"] is True
        assert "suction" in data
        assert data["suction"]["target"] == 3.0
        mock_client.get_drum_suction.assert_called_once_with(0)

    @patch('app.main.recoater_client')
    def test_set_drum_suction_success(self, mock_client):
        """Test successful drum suction pressure setting."""
        # Setup mock
        mock_client.set_drum_suction.return_value = {"status": "suction_set"}

        # Make request
        suction_data = {
            "target": 4.0
        }
        response = client.put("/api/v1/recoater/drums/0/suction", json=suction_data)

        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert data["drum_id"] == 0
        assert data["suction_command"]["target"] == 4.0
        mock_client.set_drum_suction.assert_called_once_with(
            drum_id=0, target=4.0
        )

    @patch('app.main.recoater_client')
    def test_invalid_drum_id(self, mock_client):
        """Test API with invalid drum ID."""
        # Make request with negative drum ID
        response = client.get("/api/v1/recoater/drums/-1/motion")

        # Assertions
        assert response.status_code == 422  # Validation error

    @patch('app.main.recoater_client')
    def test_invalid_motion_data(self, mock_client):
        """Test motion endpoint with invalid data."""
        # Make request with invalid motion data (missing required fields)
        motion_data = {
            "mode": "relative"
            # Missing required 'speed' field
        }
        response = client.post("/api/v1/recoater/drums/0/motion", json=motion_data)

        # Assertions
        assert response.status_code == 422  # Validation error

    @patch('app.main.recoater_client')
    def test_invalid_ejection_data(self, mock_client):
        """Test ejection endpoint with invalid data."""
        # Make request with negative target pressure
        ejection_data = {
            "target": -100.0,
            "unit": "pascal"
        }
        response = client.put("/api/v1/recoater/drums/0/ejection", json=ejection_data)

        # Assertions
        assert response.status_code == 422  # Validation error
