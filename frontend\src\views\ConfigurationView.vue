<template>
  <div class="configuration-view">
    <h2 class="view-title">Configuration</h2>
    <div class="placeholder-content">
      <p class="placeholder-text">
        Configuration panel will be implemented in Phase 4.
      </p>
      <p class="feature-list">
        <strong>Planned features:</strong>
      </p>
      <ul class="feature-list">
        <li>Build space configuration (diameter, dimensions)</li>
        <li>Resolution and matrix size settings</li>
        <li>Drum gap configuration</li>
        <li>System-level parameters</li>
        <li>Hardware connection settings</li>
        <li>Advanced system configuration</li>
      </ul>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ConfigurationView'
}
</script>

<style scoped>
.configuration-view {
  max-width: 800px;
}

.view-title {
  margin: 0 0 2rem 0;
  color: #2c3e50;
  font-size: 1.8rem;
  font-weight: 600;
}

.placeholder-content {
  background: white;
  border-radius: 8px;
  padding: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e8ed;
}

.placeholder-text {
  color: #6c757d;
  font-size: 1.1rem;
  margin-bottom: 1.5rem;
}

.feature-list {
  color: #5a6c7d;
  margin: 0.5rem 0;
}

.feature-list ul {
  margin-top: 0.5rem;
}

.feature-list li {
  margin-bottom: 0.5rem;
}
</style>
