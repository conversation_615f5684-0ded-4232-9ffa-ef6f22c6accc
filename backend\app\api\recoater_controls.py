"""
Recoater Controls API Router
============================

This module provides the recoater drum control API endpoints for the Recoater HMI.
It handles requests for drum motion, ejection pressure, and suction pressure control.
"""

from fastapi import APIRouter, HTTPException, Depends, Path
from pydantic import BaseModel, Field
from typing import Dict, Any, Literal, Optional
import logging

from services.recoater_client import RecoaterClient, RecoaterConnectionError, RecoaterAPIError

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/recoater", tags=["recoater"])

def get_recoater_client() -> RecoaterClient:
    """Dependency to get the recoater client instance."""
    from app.main import recoater_client
    if recoater_client is None:
        raise HTTPException(status_code=503, detail="Recoater client not initialized")
    return recoater_client

# Pydantic models for request/response validation

class DrumMotionRequest(BaseModel):
    """Request model for drum motion commands."""
    mode: Literal["absolute", "relative", "turns", "speed", "homing"] = Field(..., description="Motion mode")
    speed: float = Field(..., gt=0, description="Movement speed in mm/s")
    distance: Optional[float] = Field(None, description="Distance to move in mm (for absolute/relative modes)")
    turns: Optional[float] = Field(None, description="Number of turns (for turns mode)")

class DrumEjectionRequest(BaseModel):
    """Request model for drum ejection pressure commands."""
    target: float = Field(..., ge=0, description="Target ejection pressure")
    unit: Literal["pascal", "bar"] = Field(default="pascal", description="Pressure unit")

class DrumSuctionRequest(BaseModel):
    """Request model for drum suction pressure commands."""
    target: float = Field(..., ge=0, description="Target suction pressure in Pa")

# Drum Motion Endpoints

@router.get("/drums/{drum_id}/motion")
async def get_drum_motion(
    drum_id: int = Path(..., ge=0, description="The drum's ID"),
    client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """
    Get the current motion command for a drum.
    
    Args:
        drum_id: The drum's ID
        
    Returns:
        Dictionary containing current motion information
        
    Raises:
        HTTPException: If connection to recoater fails or API returns error
    """
    try:
        logger.info(f"Getting motion status for drum: {drum_id}")
        motion_data = client.get_drum_motion(drum_id)
        
        response = {
            "drum_id": drum_id,
            "motion": motion_data,
            "connected": True
        }
        
        logger.debug(f"Drum {drum_id} motion status retrieved successfully: {response}")
        return response
        
    except RecoaterConnectionError as e:
        logger.error(f"Connection error getting drum {drum_id} motion: {e}")
        raise HTTPException(status_code=503, detail=f"Connection to recoater failed: {str(e)}")
    except RecoaterAPIError as e:
        logger.error(f"API error getting drum {drum_id} motion: {e}")
        raise HTTPException(status_code=400, detail=f"Recoater API error: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error getting drum {drum_id} motion: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.post("/drums/{drum_id}/motion")
async def set_drum_motion(
    motion_request: DrumMotionRequest,
    drum_id: int = Field(..., ge=0, description="The drum's ID"),
    client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """
    Create a motion command for a drum.
    
    Args:
        drum_id: The drum's ID
        motion_request: Motion parameters (mode, speed, distance, turns)
        
    Returns:
        Dictionary containing motion command response
        
    Raises:
        HTTPException: If connection to recoater fails or API returns error
    """
    try:
        logger.info(f"Setting motion for drum {drum_id}: {motion_request}")
        
        response_data = client.set_drum_motion(
            drum_id=drum_id,
            mode=motion_request.mode,
            speed=motion_request.speed,
            distance=motion_request.distance,
            turns=motion_request.turns
        )
        
        response = {
            "drum_id": drum_id,
            "motion_command": motion_request.dict(),
            "response": response_data,
            "connected": True
        }
        
        logger.info(f"Drum {drum_id} motion command set successfully")
        return response
        
    except RecoaterConnectionError as e:
        logger.error(f"Connection error setting drum {drum_id} motion: {e}")
        raise HTTPException(status_code=503, detail=f"Connection to recoater failed: {str(e)}")
    except RecoaterAPIError as e:
        logger.error(f"API error setting drum {drum_id} motion: {e}")
        raise HTTPException(status_code=400, detail=f"Recoater API error: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error setting drum {drum_id} motion: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.delete("/drums/{drum_id}/motion")
async def cancel_drum_motion(
    drum_id: int = Field(..., ge=0, description="The drum's ID"),
    client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """
    Cancel the current motion command for a drum.
    
    Args:
        drum_id: The drum's ID
        
    Returns:
        Dictionary containing cancellation response
        
    Raises:
        HTTPException: If connection to recoater fails or API returns error
    """
    try:
        logger.info(f"Cancelling motion for drum: {drum_id}")
        response_data = client.cancel_drum_motion(drum_id)
        
        response = {
            "drum_id": drum_id,
            "action": "motion_cancelled",
            "response": response_data,
            "connected": True
        }
        
        logger.info(f"Drum {drum_id} motion cancelled successfully")
        return response
        
    except RecoaterConnectionError as e:
        logger.error(f"Connection error cancelling drum {drum_id} motion: {e}")
        raise HTTPException(status_code=503, detail=f"Connection to recoater failed: {str(e)}")
    except RecoaterAPIError as e:
        logger.error(f"API error cancelling drum {drum_id} motion: {e}")
        raise HTTPException(status_code=400, detail=f"Recoater API error: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error cancelling drum {drum_id} motion: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

# Drum Ejection Pressure Endpoints

@router.get("/drums/{drum_id}/ejection")
async def get_drum_ejection(
    drum_id: int = Field(..., ge=0, description="The drum's ID"),
    unit: Literal["pascal", "bar"] = "pascal",
    client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """
    Get the ejection pressure information for a drum.
    
    Args:
        drum_id: The drum's ID
        unit: Pressure unit ('pascal' or 'bar')
        
    Returns:
        Dictionary containing ejection pressure information
        
    Raises:
        HTTPException: If connection to recoater fails or API returns error
    """
    try:
        logger.info(f"Getting ejection pressure for drum {drum_id} in {unit}")
        ejection_data = client.get_drum_ejection(drum_id, unit)
        
        response = {
            "drum_id": drum_id,
            "ejection": ejection_data,
            "connected": True
        }
        
        logger.debug(f"Drum {drum_id} ejection pressure retrieved successfully: {response}")
        return response
        
    except RecoaterConnectionError as e:
        logger.error(f"Connection error getting drum {drum_id} ejection: {e}")
        raise HTTPException(status_code=503, detail=f"Connection to recoater failed: {str(e)}")
    except RecoaterAPIError as e:
        logger.error(f"API error getting drum {drum_id} ejection: {e}")
        raise HTTPException(status_code=400, detail=f"Recoater API error: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error getting drum {drum_id} ejection: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.put("/drums/{drum_id}/ejection")
async def set_drum_ejection(
    ejection_request: DrumEjectionRequest,
    drum_id: int = Field(..., ge=0, description="The drum's ID"),
    client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """
    Set the target ejection pressure for a drum.
    
    Args:
        drum_id: The drum's ID
        ejection_request: Ejection pressure parameters (target, unit)
        
    Returns:
        Dictionary containing ejection pressure response
        
    Raises:
        HTTPException: If connection to recoater fails or API returns error
    """
    try:
        logger.info(f"Setting ejection pressure for drum {drum_id}: {ejection_request}")
        
        response_data = client.set_drum_ejection(
            drum_id=drum_id,
            target=ejection_request.target,
            unit=ejection_request.unit
        )
        
        response = {
            "drum_id": drum_id,
            "ejection_command": ejection_request.dict(),
            "response": response_data,
            "connected": True
        }
        
        logger.info(f"Drum {drum_id} ejection pressure set successfully")
        return response
        
    except RecoaterConnectionError as e:
        logger.error(f"Connection error setting drum {drum_id} ejection: {e}")
        raise HTTPException(status_code=503, detail=f"Connection to recoater failed: {str(e)}")
    except RecoaterAPIError as e:
        logger.error(f"API error setting drum {drum_id} ejection: {e}")
        raise HTTPException(status_code=400, detail=f"Recoater API error: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error setting drum {drum_id} ejection: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

# Drum Suction Pressure Endpoints

@router.get("/drums/{drum_id}/suction")
async def get_drum_suction(
    drum_id: int = Field(..., ge=0, description="The drum's ID"),
    client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """
    Get the suction pressure information for a drum.

    Args:
        drum_id: The drum's ID

    Returns:
        Dictionary containing suction pressure information

    Raises:
        HTTPException: If connection to recoater fails or API returns error
    """
    try:
        logger.info(f"Getting suction pressure for drum: {drum_id}")
        suction_data = client.get_drum_suction(drum_id)

        response = {
            "drum_id": drum_id,
            "suction": suction_data,
            "connected": True
        }

        logger.debug(f"Drum {drum_id} suction pressure retrieved successfully: {response}")
        return response

    except RecoaterConnectionError as e:
        logger.error(f"Connection error getting drum {drum_id} suction: {e}")
        raise HTTPException(status_code=503, detail=f"Connection to recoater failed: {str(e)}")
    except RecoaterAPIError as e:
        logger.error(f"API error getting drum {drum_id} suction: {e}")
        raise HTTPException(status_code=400, detail=f"Recoater API error: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error getting drum {drum_id} suction: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@router.put("/drums/{drum_id}/suction")
async def set_drum_suction(
    suction_request: DrumSuctionRequest,
    drum_id: int = Field(..., ge=0, description="The drum's ID"),
    client: RecoaterClient = Depends(get_recoater_client)
) -> Dict[str, Any]:
    """
    Set the target suction pressure for a drum.

    Args:
        drum_id: The drum's ID
        suction_request: Suction pressure parameters (target)

    Returns:
        Dictionary containing suction pressure response

    Raises:
        HTTPException: If connection to recoater fails or API returns error
    """
    try:
        logger.info(f"Setting suction pressure for drum {drum_id}: {suction_request}")

        response_data = client.set_drum_suction(
            drum_id=drum_id,
            target=suction_request.target
        )

        response = {
            "drum_id": drum_id,
            "suction_command": suction_request.dict(),
            "response": response_data,
            "connected": True
        }

        logger.info(f"Drum {drum_id} suction pressure set successfully")
        return response

    except RecoaterConnectionError as e:
        logger.error(f"Connection error setting drum {drum_id} suction: {e}")
        raise HTTPException(status_code=503, detail=f"Connection to recoater failed: {str(e)}")
    except RecoaterAPIError as e:
        logger.error(f"API error setting drum {drum_id} suction: {e}")
        raise HTTPException(status_code=400, detail=f"Recoater API error: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error setting drum {drum_id} suction: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
