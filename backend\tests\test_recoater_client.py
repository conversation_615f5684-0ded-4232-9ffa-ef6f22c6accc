"""
Tests for RecoaterClient Service
================================

This module contains tests for the RecoaterClient service that handles
communication with the recoater hardware API.
"""

import pytest
from unittest.mock import Mock, patch
import requests
from services.recoater_client import RecoaterClient, RecoaterConnectionError, RecoaterAPIError

class TestRecoaterClient:
    """Test class for RecoaterClient service."""
    
    def setup_method(self):
        """Set up test fixtures before each test method."""
        self.base_url = "http://172.16.17.224:8080"
        self.client = RecoaterClient(self.base_url, timeout=5.0)
    
    @patch('services.recoater_client.requests.Session.request')
    def test_get_state_success(self, mock_request):
        """Test successful get_state call."""
        # Arrange
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"state": "ready", "timestamp": "2025-07-09T14:30:00Z"}
        mock_request.return_value = mock_response
        
        # Act
        result = self.client.get_state()
        
        # Assert
        assert result["state"] == "ready"
        assert result["timestamp"] == "2025-07-09T14:30:00Z"
        mock_request.assert_called_once_with(
            method="GET",
            url=f"{self.base_url}/state",
            timeout=5.0
        )
    
    @patch('services.recoater_client.requests.Session.request')
    def test_get_state_connection_error(self, mock_request):
        """Test get_state when connection fails."""
        # Arrange
        mock_request.side_effect = requests.exceptions.ConnectionError("Connection failed")
        
        # Act & Assert
        with pytest.raises(RecoaterConnectionError) as exc_info:
            self.client.get_state()
        
        assert "Failed to connect to recoater" in str(exc_info.value)
        mock_request.assert_called_once()
    
    @patch('services.recoater_client.requests.Session.request')
    def test_get_state_timeout(self, mock_request):
        """Test get_state when request times out."""
        # Arrange
        mock_request.side_effect = requests.exceptions.Timeout("Request timed out")
        
        # Act & Assert
        with pytest.raises(RecoaterConnectionError) as exc_info:
            self.client.get_state()
        
        assert "Failed to connect to recoater" in str(exc_info.value)
        mock_request.assert_called_once()
    
    @patch('services.recoater_client.requests.Session.request')
    def test_get_state_api_error(self, mock_request):
        """Test get_state when API returns error status."""
        # Arrange
        mock_response = Mock()
        mock_response.status_code = 500
        mock_response.text = "Internal Server Error"
        mock_request.return_value = mock_response
        
        # Act & Assert
        with pytest.raises(RecoaterAPIError) as exc_info:
            self.client.get_state()
        
        assert "API returned status 500" in str(exc_info.value)
        mock_request.assert_called_once()
    
    @patch('services.recoater_client.requests.Session.request')
    def test_get_config_success(self, mock_request):
        """Test successful get_config call."""
        # Arrange
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "build_space_diameter": 100,
            "resolution": 50
        }
        mock_request.return_value = mock_response
        
        # Act
        result = self.client.get_config()
        
        # Assert
        assert result["build_space_diameter"] == 100
        assert result["resolution"] == 50
        mock_request.assert_called_once_with(
            method="GET",
            url=f"{self.base_url}/config",
            timeout=5.0
        )
    
    @patch('services.recoater_client.requests.Session.request')
    def test_set_config_success(self, mock_request):
        """Test successful set_config call."""
        # Arrange
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"status": "success"}
        mock_request.return_value = mock_response
        
        config_data = {"build_space_diameter": 120, "resolution": 60}
        
        # Act
        result = self.client.set_config(config_data)
        
        # Assert
        assert result["status"] == "success"
        mock_request.assert_called_once_with(
            method="PUT",
            url=f"{self.base_url}/config",
            timeout=5.0,
            json=config_data
        )
    
    @patch('services.recoater_client.requests.Session.request')
    def test_get_drums_success(self, mock_request):
        """Test successful get_drums call."""
        # Arrange
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "drums": [
                {"id": 1, "name": "Drum 1"},
                {"id": 2, "name": "Drum 2"}
            ]
        }
        mock_request.return_value = mock_response
        
        # Act
        result = self.client.get_drums()
        
        # Assert
        assert len(result["drums"]) == 2
        assert result["drums"][0]["id"] == 1
        mock_request.assert_called_once_with(
            method="GET",
            url=f"{self.base_url}/drums",
            timeout=5.0
        )
    
    @patch('services.recoater_client.requests.Session.request')
    def test_get_drum_success(self, mock_request):
        """Test successful get_drum call."""
        # Arrange
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"id": 1, "name": "Drum 1", "status": "ready"}
        mock_request.return_value = mock_response
        
        # Act
        result = self.client.get_drum(1)
        
        # Assert
        assert result["id"] == 1
        assert result["name"] == "Drum 1"
        assert result["status"] == "ready"
        mock_request.assert_called_once_with(
            method="GET",
            url=f"{self.base_url}/drums/1",
            timeout=5.0
        )
    
    @patch('services.recoater_client.requests.Session.request')
    def test_health_check_success(self, mock_request):
        """Test successful health check."""
        # Arrange
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"state": "ready"}
        mock_request.return_value = mock_response
        
        # Act
        result = self.client.health_check()
        
        # Assert
        assert result is True
        mock_request.assert_called_once()
    
    @patch('services.recoater_client.requests.Session.request')
    def test_health_check_failure(self, mock_request):
        """Test health check when connection fails."""
        # Arrange
        mock_request.side_effect = requests.exceptions.ConnectionError("Connection failed")
        
        # Act
        result = self.client.health_check()
        
        # Assert
        assert result is False
        mock_request.assert_called_once()
    
    @patch('services.recoater_client.requests.Session.request')
    def test_non_json_response(self, mock_request):
        """Test handling of non-JSON responses."""
        # Arrange
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.side_effect = ValueError("No JSON object could be decoded")
        mock_request.return_value = mock_response
        
        # Act
        result = self.client.get_state()
        
        # Assert
        assert result == {}  # Should return empty dict for non-JSON responses
        mock_request.assert_called_once()
