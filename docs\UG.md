# Recoater HMI - User Guide

This guide explains how to use the Custom HMI to operate the Aerosint SPD Recoater.

## 1. Starting the System

### Prerequisites
- Python 3.8+ installed for backend
- Node.js 16+ installed for frontend
- Aerosint Recoater hardware (optional - see Development Mode below)

### Starting the Backend
1. Navigate to the `backend` directory
2. Install dependencies: `pip install -r requirements.txt`
3. Start the FastAPI server: `python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000`
4. Backend will be available at `http://localhost:8000`

### Starting the Frontend
1. Navigate to the `frontend` directory
2. Install dependencies: `npm install`
3. Start the development server: `npm run dev`
4. Frontend will be available at `http://localhost:5173`

### Accessing the HMI
1. Open a web browser (Chrome, Firefox, or Edge recommended)
2. Navigate to `http://localhost:5173`
3. The application will automatically connect to the backend and attempt to communicate with the recoater

## 1.1. Development Mode (No Hardware Required)

The HMI can run in development mode without requiring actual recoater hardware. This is useful for:
- Software development and testing
- Training and demonstration
- UI development without hardware access

### Enabling Development Mode
1. Navigate to the `backend` directory
2. Open the `.env` file
3. Set `DEVELOPMENT_MODE=true`
4. Start the backend as normal

### Development Mode Features
- **Mock Data**: Realistic simulated recoater data with dynamic updates
- **Full Functionality**: All UI features work with simulated responses
- **Real-time Updates**: WebSocket updates with simulated axis positions and status changes
- **Error Simulation**: Can test error handling without hardware failures
- **Temperature Simulation**: Dynamic temperature readings with realistic fluctuations
- **Progress Simulation**: Simulated job progress for testing print workflows

### Switching to Hardware Mode
1. Ensure Aerosint Recoater hardware is powered on and connected (IP: *************)
2. Set `DEVELOPMENT_MODE=false` in the `.env` file
3. Restart the backend
4. The system will now communicate with actual hardware



## 2. The Main Interface

**Note**: All features described below work in both hardware mode and development mode. In development mode, you'll see simulated data and responses instead of actual hardware values.

The interface is divided into several main tabs, accessible from a navigation menu on the left:

-   **Status:** The main landing page showing the overall system connection status and detailed information.

-   **Axis:** Manual controls for the X and Z axes (Phase 2).

-   **Recoater:** Controls for the individual drums, hoppers, and the leveler (Phase 2).

-   **Print:** For loading print files and managing the printing process (Phase 2).

-   **Configuration:** For setting system-level parameters like the build area (Phase 2).

### 2.1. Status Indicator

In the top-right corner of the header, you'll see a real-time status indicator:

- **Green dot + "Connected"**: HMI is successfully communicating with both the backend and recoater hardware
- **Red dot + "Disconnected"**: Connection to the backend has been lost
- **Orange dot + "Error"**: Connected to backend but the recoater hardware has issues

The status indicator updates automatically in real-time via WebSocket connection.

### 2.2. Status Page

The main Status page provides detailed information about the system:

**Connection Status Card:**
- Backend connection status
- Recoater hardware connection status
- Last update timestamp

**System Information Card:**
- Current recoater state (ready, printing, error, etc.)
- Additional system details when available

**Error Information Card:**
- Displays when there are connection or hardware errors
- Shows detailed error messages for troubleshooting

**Manual Refresh:**
- Use the "Refresh Status" button to manually update the status
- Automatic updates continue via WebSocket in the background



### 2.2. Using the "Axis" Window

This window allows you to manually move the machine's X and Z axes and control the punch gripper.

#### Connection Status
- The status card at the top shows the current connection state
- Green dot: Connected and operational
- Red dot: Disconnected from backend or hardware

#### Movement Parameters
1. **Distance (mm)**: Set the distance for each movement (minimum 0 mm)
2. **Speed (mm/s)**: Set the movement speed (minimum 0.1 mm/s)

#### Axis Controls

**X Axis (Horizontal Movement):**
- **Position Display**: Shows current X position in mm with 2 decimal precision
- **Status**: Indicates if axis is "Moving" or "Stopped"
- **Home Button** (`🏠`): Returns X axis to reference position
- **Left Arrow** (`←`): Moves X axis left by specified distance
- **Right Arrow** (`→`): Moves X axis right by specified distance

**Z Axis (Vertical Movement):**
- **Position Display**: Shows current Z position in mm with 2 decimal precision
- **Status**: Indicates if axis is "Moving" or "Stopped"
- **Home Button** (`🏠`): Returns Z axis to reference position
- **Up Arrow** (`↑`): Moves Z axis up by specified distance
- **Down Arrow** (`↓`): Moves Z axis down by specified distance

#### Gripper Control
- **Status Display**: Shows current gripper state (Activated/Deactivated)
- **Toggle Switch**: Click to activate or deactivate the punch gripper
- The switch position reflects the current gripper state

#### Safety Features
- All controls are automatically disabled when disconnected from hardware
- Movement buttons are disabled when the respective axis is already moving
- Homing operations take priority and disable other movements
- Error messages are displayed for failed operations

#### Recommended Workflow
1. **Check Connection**: Ensure the status shows "Connected"
2. **Home Axes**: Press the home button (`🏠`) for both X and Z axes before starting operations
3. **Set Parameters**: Enter desired distance and speed values
4. **Move Axes**: Use directional arrows to position the axes as needed
5. **Control Gripper**: Activate gripper when needed for part handling

#### Real-time Updates
- Axis positions update automatically every second via WebSocket connection
- Movement status changes are reflected immediately in the interface
- Connection status updates in real-time

### 2.3. Using the "Recoater" Window

This window is for preparing the recoater drums.

1.  **Select a Component:** Click on the graphical representation of a drum, hopper, or leveler to show its specific controls on the right.

2.  **Drum Control:**

    -   Set the desired `Suction` and `Ejection` pressures.

    -   To manually rotate a drum, set a `Speed` and a number of `Turns`, then press the "Rotate" button (`🔄`).

3.  **Hopper Control (Scraping Blade):**

    -   Select which screw to move (`Front`, `Back`, or `Both`).

    -   Enter a `Distance (µm)` and use the up/down arrows to move the blade.

    -   **Always Home the blade (`🏠`) on startup.**



### 2.4. Running a Print Job ("Print" Window)

This is the main window for executing a print.

1.  **Load Geometry:** For each drum you intend to use, click the "Upload" button (`⬆️`) and select the appropriate `.CLI` or `.PNG` file for that material.

2.  **Set Parameters:** On the right-hand panel, configure all print parameters, such as `Patterning speed`, `Filling drum`, and `Layer thickness`.

3.  **Preview:** The central area will show a preview of the deposited powder based on your loaded files.

4.  **Start Printing:** Once everything is configured, press the **"Start Printing"** button.

5.  **Stop Printing:** To cancel the active job, press the **"Stop Printing"** button.

