/**
 * Status Store
 * ============
 * 
 * Pinia store for managing the application's connection status and real-time
 * updates from the backend via WebSocket.
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import apiService from '../services/api'

export const useStatusStore = defineStore('status', () => {
  // State
  const isConnected = ref(false)
  const recoaterStatus = ref(null)
  const axisData = ref(null)
  const drumData = ref(null)
  const lastError = ref(null)
  const lastUpdate = ref(null)
  const websocket = ref(null)
  
  // Computed
  const isHealthy = computed(() => {
    return isConnected.value && recoaterStatus.value && !lastError.value
  })

  const connected = computed(() => isConnected.value)
  const backendHealthy = computed(() => isConnected.value && !lastError.value)
  
  // Actions
  function updateStatus(statusData) {
    recoaterStatus.value = statusData
    lastUpdate.value = new Date()
    lastError.value = null
  }

  function updateAxisData(newAxisData) {
    axisData.value = newAxisData
    lastUpdate.value = new Date()
  }

  function updateDrumData(data) {
    drumData.value = data
    lastUpdate.value = new Date()
  }
  
  function setError(error) {
    lastError.value = error
    lastUpdate.value = new Date()
  }
  
  function setConnectionStatus(connected) {
    isConnected.value = connected
    if (!connected) {
      recoaterStatus.value = null
      lastError.value = 'Connection lost'
    }
  }
  
  async function fetchStatus() {
    try {
      const response = await apiService.getStatus()
      updateStatus(response.data)
      setConnectionStatus(true)
      return response.data
    } catch (error) {
      console.error('Failed to fetch status:', error)
      setError(error.message || 'Failed to fetch status')
      setConnectionStatus(false)
      throw error
    }
  }
  
  function connectWebSocket() {
    if (websocket.value) {
      return // Already connected
    }
    
    const wsUrl = `ws://localhost:8000/ws`
    console.log('Connecting to WebSocket:', wsUrl)
    
    try {
      websocket.value = new WebSocket(wsUrl)
      
      websocket.value.onopen = () => {
        console.log('WebSocket connected')
        setConnectionStatus(true)
      }
      
      websocket.value.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data)
          console.log('WebSocket message received:', message)
          
          if (message.type === 'status_update') {
            updateStatus(message.data)
            if (message.axis_data) {
              updateAxisData(message.axis_data)
            }
            if (message.drum_data) {
              updateDrumData(message.drum_data)
            }
          } else if (message.type === 'connection_error') {
            setError(message.error)
          }
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error)
        }
      }
      
      websocket.value.onclose = () => {
        console.log('WebSocket disconnected')
        setConnectionStatus(false)
        websocket.value = null
        
        // Attempt to reconnect after 3 seconds
        setTimeout(() => {
          if (!websocket.value) {
            connectWebSocket()
          }
        }, 3000)
      }
      
      websocket.value.onerror = (error) => {
        console.error('WebSocket error:', error)
        setError('WebSocket connection error')
      }
      
    } catch (error) {
      console.error('Failed to create WebSocket:', error)
      setError('Failed to establish WebSocket connection')
    }
  }
  
  function disconnectWebSocket() {
    if (websocket.value) {
      websocket.value.close()
      websocket.value = null
    }
    setConnectionStatus(false)
  }
  
  return {
    // State
    isConnected,
    recoaterStatus,
    axisData,
    drumData,
    lastError,
    lastUpdate,

    // Computed
    isHealthy,
    connected,
    backendHealthy,

    // Actions
    updateStatus,
    updateAxisData,
    updateDrumData,
    setError,
    setConnectionStatus,
    fetchStatus,
    connectWebSocket,
    disconnectWebSocket
  }
})
